/* Main CSS Entry Point - Imports all style modules */

/* Base styles - Reset and core styles (must be first) */
@import './base.css';

/* Layout styles - Container and positioning */
@import './layout.css';

/* Component styles - Buttons and UI elements */
@import './components.css';

/* Animation utilities - General purpose animations */
@import './animations.css';

/* Transition styles - Page transitions and view transitions */
@import './transitions.css';

/* Responsive styles - Media queries (should be last) */
@import './responsive.css';

/* Layout styles - Container and positioning */
@import './layout.css';

/* Typography styles - Text elements */
@import './typography.css';

/* Component styles - Buttons and UI elements */
@import './components.css';

/* Microphone styles - All microphone button variations */
@import './microphone.css';

/* Recording styles - Recording UI and feedback */
@import './recording.css';

/* Animation utilities - General purpose animations */
@import './animations.css';

/* Transition styles - Page transitions and view transitions */
@import './transitions.css';
